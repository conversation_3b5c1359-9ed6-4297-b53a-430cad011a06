import yfinance as yf
from colorama import Fore
from mcp.server.fastmcp import FastMCP
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

# Create both MCP server and FastAPI app
mcp = FastMCP("yfinanceserver")
app = FastAPI()


@mcp.tool()
def stock_price(stock_ticker: str) -> str:
    dat = yf.Ticker(stock_ticker)
    historical_prices = dat.history(period='1mo')
    last_months_closes = historical_prices['Close']

    print(Fore.YELLOW + str(last_months_closes))
    return str(f"Stock price for {stock_ticker} is {last_months_closes}")

# Web interface routes


@app.get("/", response_class=HTMLResponse)
async def home():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Finance Server</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .form-group { margin: 20px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input[type="text"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
            button { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
            button:hover { background-color: #0056b3; }
            .result { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff; }
            .error { border-left-color: #dc3545; background-color: #f8d7da; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Finance Server - Stock Price Lookup</h1>
            <form id="stockForm">
                <div class="form-group">
                    <label for="ticker">Stock Ticker Symbol:</label>
                    <input type="text" id="ticker" name="ticker" placeholder="e.g., AAPL, GOOGL, MSFT" required>
                </div>
                <button type="submit">Get Stock Price</button>
            </form>
            <div id="result"></div>
        </div>

        <script>
            document.getElementById('stockForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                const ticker = document.getElementById('ticker').value;
                const resultDiv = document.getElementById('result');

                resultDiv.innerHTML = '<p>Loading...</p>';

                try {
                    const response = await fetch(`/stock/${ticker}`);
                    const data = await response.json();

                    if (response.ok) {
                        resultDiv.innerHTML = `<div class="result"><h3>Stock Data for ${ticker.toUpperCase()}</h3><pre>${data.result}</pre></div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="result error"><h3>Error</h3><p>${data.error}</p></div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="result error"><h3>Error</h3><p>Failed to fetch data: ${error.message}</p></div>`;
                }
            });
        </script>
    </body>
    </html>
    """


@app.get("/stock/{ticker}")
async def get_stock_price(ticker: str):
    try:
        result = stock_price(ticker)
        return {"ticker": ticker, "result": result}
    except Exception as e:
        return {"error": str(e)}, 400

# MCP server endpoint


@app.get("/mcp")
async def mcp_endpoint():
    return {"message": "MCP server is running", "tools": ["stock_price"]}

if __name__ == "__main__":
    # Run the web server instead of MCP server for browser access
    uvicorn.run(app, host="127.0.0.1", port=8000)
